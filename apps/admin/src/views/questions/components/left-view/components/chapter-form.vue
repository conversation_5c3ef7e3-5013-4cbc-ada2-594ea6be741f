<script setup lang="ts">
import { computed } from 'vue'
import { useNaiveForm } from '@sa/hooks'

defineOptions({
  name: 'ChapterForm',
})

// 定义 props
const props = defineProps<{
  chapterOptions: QuestionsApi.GetChapterListResponse[]
  allFlatNodes: Array<{ key: string, label: string }>
  onSelectKnowledgePoint: () => void
}>()

const modelInfo = defineModel('modelInfo', {
  type: Object as PropType<{
    ChapterName: string
    AdditionalRequirements: string
    KnowledgePointIds: string[]
  }>,
  default: () => ({
    ChapterName: '',
    AdditionalRequirements: '',
    KnowledgePointIds: [],
  }),
})

// 表单引用和校验
const { formRef, validate } = useNaiveForm()

// 表单校验规则
const rules = {
  ChapterName: [
    {
      required: true,
      message: '请输入章节名称',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: string) => {
        if (!value || value.trim() === '') {
          return new Error('请输入章节名称')
        }
        if (value.trim().length < 2) {
          return new Error('章节名称至少需要2个字符')
        }
        return true
      },
    },
  ],
}

// 已选择的章节名称列表
const selectedChapterNames = computed(() => {
  return modelInfo.value.KnowledgePointIds
    .map(id => props.allFlatNodes.find(node => node.key === id))
    .filter(Boolean) as Array<{ key: string, label: string }>
})

// 移除已选择的项目
function removeSelectedItem(key: string) {
  const index = modelInfo.value.KnowledgePointIds.indexOf(key)
  if (index > -1) {
    modelInfo.value.KnowledgePointIds.splice(index, 1)
  }
}

// 暴露校验方法给父组件
defineExpose({
  validate,
})

function selectKnowledgePoint() {
  props.onSelectKnowledgePoint()
}
</script>

<template>
  <div class="p-12px">
    <NForm ref="formRef" :model="modelInfo" :rules="rules">
      <!-- 出题范围标题 -->
      <div class="flex items-center">
        <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
        <span class="text-14px text-[#333] font-500">出题范围</span>
      </div>

      <!-- 知识点选择区域 -->
      <NFormItem path="ChapterName">
        <!-- 如果没有选择知识点，显示选择区域 -->
        <div
          v-if="selectedChapterNames.length === 0"
          class="min-h-200px w-full flex flex-col cursor-pointer items-center justify-center border-2 border-gray-300 rounded-8px border-dashed bg-gray-50 p-24px transition-all duration-200 hover:border-blue-400 hover:bg-blue-50"
          @click="selectKnowledgePoint"
        >
          <div class="h-full flex flex-col items-center justify-center">
            <!-- 加号图标 -->
            <div class="mb-12px h-48px w-48px flex items-center justify-center rounded-full bg-blue-500 text-white">
              <SvgIcon icon="mdi:plus" class="text-24px" />
            </div>
            <!-- 提示文字 -->
            <span class="text-14px text-gray-600 font-500">选择章节目录</span>
          </div>
        </div>

        <!-- 如果已选择知识点，显示已选择的列表 -->
        <div v-else class="w-full">
          <!-- 顶部操作栏 -->
          <div class="mb-12px flex items-center justify-between">
            <span class="text-14px text-gray-600">
              已选择 {{ selectedChapterNames.length }} 个知识点
            </span>
            <NButton size="small" type="primary" @click="selectKnowledgePoint">
              <template #icon>
                <SvgIcon icon="mdi:plus" />
              </template>
              添加知识点
            </NButton>
          </div>

          <!-- 已选择的知识点列表 -->
          <div class="border border-gray-200 rounded-8px bg-gray-50 p-12px">
            <div class="flex flex-wrap gap-8px">
              <div
                v-for="item in selectedChapterNames"
                :key="item.key"
                class="relative inline-block rounded-6px bg-white px-12px py-6px shadow-sm"
              >
                <span class="whitespace-nowrap text-14px text-gray-700" :title="item.label">
                  {{ item.label }}
                </span>
                <NButton
                  size="tiny"
                  quaternary
                  type="error"
                  class="absolute h-16px w-16px rounded-full -right-2px -top-2px"
                  @click="removeSelectedItem(item.key)"
                >
                  <template #icon>
                    <SvgIcon icon="mdi:close" class="text-12px" />
                  </template>
                </NButton>
              </div>
            </div>
          </div>
        </div>
      </NFormItem>

      <!-- 补充内容区域 -->
      <div class="border border-gray-200 rounded-8px bg-white">
        <!-- 标题栏 -->
        <div
          class="flex items-center justify-between p-16px"
        >
          <span class="text-14px text-[#464646] font-500">补充内容（选填）</span>
        </div>

        <!-- 可折叠内容 -->
        <div
          class="overflow-hidden transition-all duration-300"
        >
          <div class="border-t border-gray-200 p-16px">
            <NInput
              v-model:value="modelInfo.AdditionalRequirements"
              type="textarea"
              placeholder="您可以补充特殊的出题要求。&#10;示例如下：&#10;题目中需要包含&quot;折射&quot;&quot;反射&quot;这两个词汇"
              :rows="4"
              clearable
              maxlength="500"
              show-count
              class="w-full"
            />
          </div>
        </div>
      </div>
    </NForm>
  </div>
</template>
